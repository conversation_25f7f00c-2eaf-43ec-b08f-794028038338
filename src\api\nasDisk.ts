import request from "@/request";
import { PoolInfoResponse } from "./fatWall";

export interface ApiResponse<T> {
  code: number;
  result: string;
  data: T;
}

// 文件/文件夹项目定义
export interface FileItem {
  name: string;
  size: string;
  parent: string;
  modified_time: string;
  xattr: {
    directory?: boolean;
    favorite?: boolean;
    media_duration?: number;
  };
}

// 分页参数
export interface PageParams {
  size?: number;
  token?: string;
}

// 排序参数
export interface OrderParams {
  basis?: 'name' | 'size' | 'modified_time' | 'type';
  desc?: boolean;
}

// 路径参数
export interface PathParams {
  parent: string;
  recursion: boolean;
}

// 目录列表入参
export interface ListDirectoryParams {
  page?: PageParams;
  order?: OrderParams;
  path: PathParams;
}

// 目录列表返回数据分页信息
export interface PageResponse {
  size: number;
  token: string;
}

// 目录列表返回数据
export interface DirectoryListResponse {
  page: PageResponse;
  files: FileItem[];
}

/**
 * 列出指定路径下子文件夹/文件
 * @param params 请求参数
 * @returns Promise<ApiResponse<DirectoryListResponse>>
 */
export const listDirectory = (
  params: ListDirectoryParams
): Promise<ApiResponse<DirectoryListResponse>> => {
  return request.post('/filemgr/list_directory', params);
};

// 百度网盘文件列表请求参数
export interface BaiduListParams {
  action: string; // 固定为remotelist
  path?: string; // 需要list的目录，以/开头的绝对路径, 默认为/
  order?: string; // 排序字段：默认为name；time按修改时间排序；name表示按文件名称排序；size表示按文件大小排序
  desc?: number; // 默认为升序(0)，设置为1实现降序
  start?: number; // 起始位置，从0开始
  limit?: number; // 查询数目，默认为1000
  web?: number; // 值为1时，返回dir_empty属性和缩略图数据
  folder?: number; // 是否只返回文件夹，0返回所有，1只返回文件夹
  showempty?: number; // 是否返回dir_empty属性，0不返回，1返回
}

// 百度网盘文件项
export interface BaiduFileItem {
  fs_id: number;
  path: string;
  server_filename: string;
  size: number;
  server_mtime: number;
  server_ctime: number;
  local_mtime: number;
  local_ctime: number;
  isdir: number; // 0 文件、1 目录
  category: number; // 1 视频、2 音频、3 图片、4 文档、5 应用、6 其他、7 种子
  md5?: string; // 只有文件类型时存在
  dir_empty?: number; // 该目录是否存在子目录，0为存在，1为不存在
  thumbs?: {
    icon?: string;
    url1?: string;
    url2?: string;
    url3?: string;
    [key: string]: string | undefined;
  }; // 图片缩略图URLs，仅category=3时存在
  tkbind_id: number;
  unlist: number;
  wpfile: number;
  share: number;
  pl: number;
  empty: number;
  is_scene: number;
  extent_int2: number;
  extent_tinyint7: number;
  owner_id: number;
  extent_int8: number;
  owner_type: number;
  from_type: number;
  server_atime: number;
  oper_id: number;
  real_category: string;
}

// 百度网盘文件列表响应
export interface BaiduListResponse {
  errno: number;
  guid_info: string;
  list: BaiduFileItem[];
  request_id: number;
  guid: number;
  code?: number;
  message?: string;
}

/**
 * 通用的百度网盘
 * @param endpoint 接口路径
 * @param params 请求参数
 * @returns Promise<any>
 */
const baiduPanRequest = <T>(params: any,config?: { loadingMode?: 'full' | 'icon'; showLoading?: boolean }): Promise<T> => {
  // 如果明确指定了showLoading，使用指定值；否则根据loadingMode决定
  const requestConfig = config?.showLoading !== undefined
    ? { showLoading: config.showLoading }
    : config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: true }; // 默认显示loading

  // 判断当前环境
  if (process.env.NODE_ENV === 'development') {
    // 开发环境使用代理
    return request.post('/baidupan/baidupan.cgi', params, {
      baseURL: '',
      ...requestConfig
    });
  } else {
    // 生产环境中使用HTTP协议 使用完整的主机名（包含端口号）
    const host = window.location.host;

    // 从URL路径中提取ID
    let pluginId = '2510237042'; // 默认ID
    const pathMatch = window.location.pathname.match(/\/plugin\/(\d+)/);
    if (pathMatch && pathMatch[1]) {
      pluginId = pathMatch[1];
    }

    return request.post(`/plugin/${pluginId}/baidupan/baidupan.cgi`, params, {
      baseURL: `http://${host}`,
      ...requestConfig
    });
  }
};

/**
 * 获取百度网盘文件列表
 * @param params 请求参数
 * @returns Promise<BaiduListResponse>
 */
export const getBaiduNetdiskFileList = (
  params: BaiduListParams,
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<BaiduListResponse> => {
  return baiduPanRequest<BaiduListResponse>(params,config);
};

// 百度网盘创建文件夹请求参数
export interface BaiduCreateDirParams {
  action: string; // 固定为createdir
  path: string; // 要创建的文件夹路径，以/开头的绝对路径
}

// 百度网盘创建文件夹响应
export interface BaiduCreateDirResponse {
  errno: number;
  code: number;
  errmsg?: string;
  request_id?: number;
}

/**
 * 创建百度网盘文件夹
 * @param params 请求参数
 * @returns Promise<BaiduCreateDirResponse>
 */
export const createBaiduNetdiskFolder = (
  params: BaiduCreateDirParams
): Promise<BaiduCreateDirResponse> => {
  return baiduPanRequest<BaiduCreateDirResponse>(params);
};

// 百度网盘上传路径项
export interface BaiduUploadPathItem {
  type: 'file' | 'directory'; // 是否为目录：file 文件 directory 目录
  path: string; // 文件或目录路径
}

// 百度网盘上传请求参数
export interface BaiduUploadParams {
  action: string; // 任务类型：upload 上传
  autotask: number; // 是否为自动任务：1 是 0 否（只有目录才支持设置为自动任务）
  remotepath: string; // 用户百度网盘文件路径
  localpath: BaiduUploadPathItem[]; // 用户本地路径数组
}

// 百度网盘上传响应
export interface BaiduUploadResponse {
  code: number;
  errno?: number;
  errmsg?: string;
  request_id?: number;
  task_id?: string; // 任务ID
}

/**
 * 上传文件到百度网盘（自动任务）
 * @param params 请求参数
 * @returns Promise<BaiduUploadResponse>
 */
export const uploadToBaiduNetdisk = (
  params: BaiduUploadParams
): Promise<BaiduUploadResponse> => {
  return baiduPanRequest<BaiduUploadResponse>(params);
};

// 百度网盘下载路径项
export interface BaiduDownloadPathItem {
  type: 'file' | 'directory'; // 是否为目录：file 文件 directory 目录
  path: string; // 文件或目录路径
}

// 百度网盘下载请求参数
export interface BaiduDownloadParams {
  action: string; // 任务类型：download 下载
  autotask: number; // 是否为自动任务：1 是 0 否（只有目录才支持设置为自动任务）
  remotepath: BaiduDownloadPathItem[]; // 用户百度网盘文件路径项数组
  localpath: string; // 用户本地存储路径
}

// 百度网盘下载响应
export interface BaiduDownloadResponse {
  code: number; // 0表示成功，非0表示失败
  result: string; // 成功/失败
  failed_paths?: string[]; // 失败的路径列表，仅在失败时存在
  task_id?: string; // 任务ID
}

/**
 * 从百度网盘下载文件
 * @param params 请求参数
 * @returns Promise<BaiduDownloadResponse>
 */
export const downloadFromBaiduNetdisk = (
  params: BaiduDownloadParams
): Promise<BaiduDownloadResponse> => {
  return baiduPanRequest<BaiduDownloadResponse>(params);
};

// 任务信息相关接口定义

// 选择器类型
export interface TaskSelector {
  key: 'task_id' | 'module' | 'type' | 'tasktype';
  value: string[];
}

// 分页参数
export interface TaskPageParams {
  size: number;
  id: number;
}

// 获取任务信息请求参数
export interface GetTaskInfoParams {
  selector?: TaskSelector[];
  page?: TaskPageParams;
}

// 任务详情
export interface TaskDetail {
  handle_size: string | number | null | undefined;
  progress: number;
  total_files: number;
  handled_files: number;
  total_size: string;
  handled_size: string;
  handling_file: string;
  speed: string;
  total_file_cnt: number;
  finish_file_cnt:number;
  finish_time: string
}

// 任务信息
export interface TaskInfo {
  task_id: string;
  src: string[];
  src_type: string; // file/dir，用于客户端图标显示
  dst: string;
  status: 'waiting' | 'running' | 'paused' | 'success' | 'failed' | 'cancelled' | 'partial error' | 'success_waiting';
  module: string;
  create_time: string; // 毫秒时间戳
  done_time: string; // 毫秒时间戳
  action: string;
  detail: TaskDetail;
}

// 任务分页信息
export interface TaskPageResponse {
  size: number;
  total_page: number;
  total_task: number;
}

// 获取任务信息响应数据
export interface GetTaskInfoData {
  page: TaskPageResponse;
  info: TaskInfo[];
}

// 获取任务信息响应
export interface GetTaskInfoResponse {
  code: number;
  result: string;
  data: GetTaskInfoData;
}

/**
 * 获取任务信息
 * @param params 请求参数
 * @returns Promise<GetTaskInfoResponse>
 */
export const getTaskInfo = (
  params: GetTaskInfoParams
): Promise<GetTaskInfoResponse> => {
  return request.post('/taskcenter/get_taskinfo', params);
};

// 控制任务请求参数
export interface ControlTaskParams {
  task_id: string[];
  command: 'pause' | 'continue' | 'cancel' | 'restart';
}

// 控制任务响应
export interface ControlTaskResponse {
  code: number;
  result: string;
}

/**
 * 控制任务（暂停/继续/取消/重启）
 * @param params 请求参数
 * @returns Promise<ControlTaskResponse>
 */
export const controlTask = (
  params: ControlTaskParams
): Promise<ControlTaskResponse> => {
  return request.post('/taskcenter/ctrl_task', params);
};

// 删除历史任务选择器
export interface DeleteHistorySelector {
  key: 'task_id';
  value: string[];
}

// 删除历史任务请求参数
export interface DeleteHistoryParams {
  selector: DeleteHistorySelector[];
}

// 删除历史任务响应
export interface DeleteHistoryResponse {
  code: number;
  result: string;
}

/**
 * 删除历史任务
 * @param params 请求参数
 * @returns Promise<DeleteHistoryResponse>
 */
export const deleteHistoryTask = (
  params: DeleteHistoryParams
): Promise<DeleteHistoryResponse> => {
  return request.post('/taskcenter/delete_task', params);
};

// 统一删除任务选择器
export interface DeleteTaskSelector {
  key: 'task_id';
  value: string[];
}

// 统一删除任务请求参数
export interface DeleteTaskParams {
  selector: DeleteTaskSelector[];
}

// 统一删除任务响应
export interface DeleteTaskResponse {
  code: number;
  result: string;
}

/**
 * 统一删除任务（支持删除所有状态的任务）
 * @param params 请求参数
 * @returns Promise<DeleteTaskResponse>
 */
export const deleteTask = (
  params: DeleteTaskParams
): Promise<DeleteTaskResponse> => {
  return request.post('/taskcenter/delete_task', params);
};

// 百度网盘用户信息请求参数
export interface BaiduUserInfoParams {
  action: string; // 固定为uinfo
}

// 百度网盘用户信息响应
export interface BaiduUserInfoResponse {
  avatar_url: string;
  baidu_name: string;
  errmsg: string;
  errno: number;
  netdisk_name: string;
  request_id: string;
  uk: number;
  vip_type: number;
  nas_vip: number;
  total: number;
  used: number;
  token:string
  end_time:string
  code?: number; // 添加code字段，用于错误处理
}

/**
 * 获取百度网盘用户信息
 * @param params 请求参数
 * @returns Promise<BaiduUserInfoResponse>
 */
export const getBaiduUserInfo = (
  params: BaiduUserInfoParams
): Promise<BaiduUserInfoResponse> => {
  // 使用静默更新，不显示loading
  return baiduPanRequest<BaiduUserInfoResponse>(params, { showLoading: false });
};

// 百度网盘设备绑定请求参数
export interface DeviceBindParams {
  method: string; // 固定为bind
  access_token: string; // 接口鉴权参数
  device_id: string; // 设备ID，设备注册接口下发
  device_name?: string; // 设备名称，用于展示在网盘"智能设备"中
}

// 百度网盘设备绑定响应
export interface DeviceBindResponse {
  errno: number;
  errmsg: string;
  request_id: string;
  last_user?: string; // 上一个绑定用户的账号，errno=42103时有效
}

/**
 * 百度网盘设备绑定
 * @param params 请求参数
 * @returns Promise<DeviceBindResponse>
 */
export const bindBaiduNetdiskDevice = (
  params: DeviceBindParams
): Promise<DeviceBindResponse> => {
  // 使用代理路径
  const url = '/baidu-api/rest/2.0/xpan/device';

  // 将参数作为URL查询参数传递
  return request.get(url, params);
};

// 百度网盘解绑请求参数
export interface BaiduUnbindParams {
  action: string; // 固定为unbind
}

// 百度网盘解绑响应
export interface BaiduUnbindResponse {
  code: number;
  result: string;
}

/**
 * 解绑百度网盘账号
 * @param params 请求参数
 * @returns Promise<BaiduUnbindResponse>
 */
export const unbindBaiduNetdisk = (
  params: BaiduUnbindParams
): Promise<BaiduUnbindResponse> => {
  return baiduPanRequest<BaiduUnbindResponse>(params);
};

// 获取存储池信息
export const getPoolInfo = (): Promise<ApiResponse<PoolInfoResponse>> => {
  return request.post('/filemgr/get_pool_info', {}, { showLoading: false });
};

.cameraManagementContainer {
  // padding: 10px;
  // background-color: #f7f7f7;
  background-color: var(--home-background-color);
  padding-bottom: 10px;
  min-height: calc(100vh - 35px); // 减去顶部导航栏的高度
  height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: var(--background-color);
  }

  .backIcon,
  .infoIcon,
  .addIcon {
    width: 40px;
    height: 40px;
    background-color: var(--background-color);
  }

  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 26px;
    background-color: var(--background-color);
  }

  .iconContainer {
    align-items: center;
  }

  .scrollContainer {
    flex: 1;
    height: calc(100% - 115px);
    scrollbar-width: none;
    overflow-y: auto;
  }
  .eventReplayContainer {
    background-color: var(--card-background-color);
    padding: 4px 16px 4px;
    border-radius: 8px;
    margin: 12px 12px 0;

    :global {
      .adm-list-item {
        background-color: transparent !important;
      }
    }

    .all {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .viewAll {
        display: flex;
        align-items: center;
        color: var(--list-value-text-color);
        .view {
          font-size: 14px;
        }
        :global(.adm-image) {
          margin-top: 0;
        }
      }
    }
  }

  .eventReplayItem {
    padding: 0;
    margin-bottom: 10px;

    // :global{
    //   .adm-list-item-content-main{
    //     background-color: var(--card-background-color);
    //   }
    // }
  }

  .eventReplayTitle {
    color: var(--text-color);
    margin-bottom: 5px;
    font-size: 17px;
    font-weight: 500;
  }

  .eventReplayImages {
    display: flex;
    overflow-x: auto;
    justify-content: space-between;
    margin-top: 10px;
  }

  .eventReplayEmpty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 72px;
    width: 100%;
  }

  .emptyText {
    color: var(--list-value-text-color);
    font-size: 14px;
  }

  .eventReplayImageItem {
    width: 108px;
    height: 72px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
  }

  .eventReplayImage {
    object-fit: cover;
  }

  .eventReplayTime {
    position: absolute;
    bottom: 4px;
    left: 5px;
    // background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 12px;
    padding: 2px;
    border-radius: 8px;
    display: flex;
    align-items: center;
  }

  .playIcon {
    width: 16px;
    height: 16px;
    margin-right: 2px;
  }

  .storageManagementContainer {
    margin: 8px 12px;

    .storageManagementButton {
      width: 100%;
      font-size: 14px;
      background-color: var(--card-background-color);
      border: none;
      border-radius: 16px;
      padding: 22px 24px;
      text-align: left;

      .icon {
        margin-right: 16px;
        position: relative;
        top: 5px;
        width: 24px;
        height: 24px;
      }

      .text {
        font-size: 16px;
        font-weight: 500;
        color: var(--button-text-color);
      }
    }

    :global {
      .adm-button::before {
        border-radius: 16px;
      }
    }
  }

  .functionButtonsContainer {
    display: flex;
    justify-content: space-between;
    margin: 8px 12px;
    .aiButton,
    .deviceButton {
      flex: 1;
      margin-right: 10px;
      font-size: 14px;
      background-color: var(--card-background-color);
      border: none;
      border-radius: 16px;
      padding: 19px 24px;
      box-sizing: content-box;
      text-align: left;

      &:last-child {
        margin-right: 0;
      }
    }

    .icon {
      margin-right: 16px;
      position: relative;
      top: 5px;
      width: 24px;
      height: 24px;
    }

    .text {
      font-size: 16px;
      font-weight: 500;
      color: var(--button-text-color);
    }
    :global {
      .adm-button::before {
        border-radius: 16px;
      }
    }
  }
}

// 骨架屏样式
.skeletonContainer {
  padding: 0;
}

.eventSkeletonContainer {
  background-color: var(--card-background-color);
  padding: 16px;
  border-radius: 8px;
  margin: 16px;
  margin-top: 20px;
}

.eventSkeletonHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.eventSkeletonImages {
  display: flex;
  gap: 8px;
}

.cameraItem {
  width: 100%;
  height: 110px;
  img{
    width: 100%;
    height: 100%;
  }
}

.viewIcon {
  height: 18px;
  margin-left: 8px;
}

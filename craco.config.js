const path = require("path");
const TerserPlugin = require("terser-webpack-plugin");
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer"); // 新增分析工具

module.exports = {
  webpack: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 确保路径正确
    },
    plugins: [
      new CompressionWebpackPlugin({
        filename: "[path][base].gz",
        algorithm: "gzip",
        test: /\.(js|css|html|svg)$/,
        threshold: 10240,
        minRatio: 0.8,
        deleteOriginalAssets: false, // 不删除源文件
      }),
      new BundleAnalyzerPlugin({
        analyzerMode: "static",
        openAnalyzer: false,
      })
    ],
    configure: (webpackConfig) => {
      if (webpackConfig.mode === "production") {
        // 移除 Source Map
        webpackConfig.devtool = false;

        webpackConfig.optimization = {
          ...webpackConfig.optimization,
          minimizer: [
            // 配置 TerserPlugin
            new TerserPlugin({
              terserOptions: {
                compress: {
                  drop_console: false, // 移除 console.log
                  drop_debugger: true, // 移除 debugger
                  dead_code: true, // 移除死代码
                  unused: true, // 移除未使用变量
                },
                mangle: true, // 混淆变量名
                module: true, // 优化es6
                format: {
                  comments: false, // 移除所有注释
                },
              },
              parallel: true, // 并行压缩
              extractComments: false, // 不提取注释到单独文件
            }),
          ],
          // 新增代码分割配置
          // splitChunks: {
          //   chunks: "all",
          //   minSize: 100000, // 生成 chunk 的最小体积（约100kb）
          //   maxSize: 244000, // 尝试将大于 maxSize 的 chunk 分割
          //   minChunks: 1, // 被引用次数
          //   maxAsyncRequests: 5, // 按需加载时的最大并行请求数
          //   maxInitialRequests: 3, // 入口点的最大并行请求数
          //   automaticNameDelimiter: "~",
          //   cacheGroups: {
          //     vendors: {
          //       test: /[\\/]node_modules[\\/]/,
          //       priority: -10,
          //       name: "vendors",
          //       chunks: "all",
          //       maxSize: 244000,
          //     },
          //     common: { // 新增common组
          //       name: "common",
          //       minChunks: 3, // 至少被3个chunk引用
          //       priority: -20,
          //       reuseExistingChunk: true,
          //     },
          //     default: {
          //       minChunks: 2,
          //       priority: -30,
          //       reuseExistingChunk: true,
          //     },
          //   },
          // },
          usedExports: true, // 标记未使用的导出
        }
      }
      return webpackConfig;
    },
  },
  style: {
    modules: {
      localIdentName: "[name]__[local]___[hash:base64:5]",
    },
    postcss: {
      mode: "extends",
      loaderOptions: {
        postcssOptions: {
          plugins: [
            [
              "postcss-pxtorem",
              {
                rootValue: 39.2, // 设计稿宽度/10（如 375px 设计稿设为 37.5）
                propList: ["*"], // 转换所有 CSS 属性的 px 单位
                unitPrecision: 5, // 保留小数点位数
                minPixelValue: 1, // 最小转换像素值
                exclude: /node_modules/i,
                selectorBlackList: ["ignore-"],
                mediaQuery: false,
                replace: true
              },
            ],
          ],
        },
      },
    },
  },
  // 添加开发服务器代理配置
  devServer: {
    proxy: {
      '/baidupan': {
        target: 'http://192.168.0.221/plugin/**********',
        changeOrigin: true,
      },
      '/timemachine': {
        target: 'http://192.168.33.221/plugin/**********',
        changeOrigin: true
      }
    }
  }
};

import { FC, useRef, useEffect } from "react";
import styles from "./index.module.scss";
import { LeftOutlined } from "@ant-design/icons";
import poster_test from "@/Resources/icon/backIcon_light.png";

import { useHistory } from "react-router-dom";
import FilterFilmCard from "../../../../components/FATWall_TV/FilterFilmCard";
import TVFocusable from "../TVFocus";


interface VideoDetailsProps {
    videoId?: string;
}

// 默认数据，确保即使API未返回数据也有值可用
const defaultVideoData = {
    title: "纸牌屋",
    rating: 8.7,
    year: 2022,
    category: "美食",
    region: "日本",
    duration: "24分钟/集",
    tags: ["4K", "HDR10+", "Dolby Atmos"],
    description: "《孤独的美食家》是一部日本美食纪录片风格的电视剧，深受广大观众的喜爱。松重丰饰演的主角\"井之头五郎\"，是个深居简出的小贩,广大观众的喜爱。松重丰饰演的主角\"井之头五郎\"，是个深居简出的小贩,广大观众的喜爱。松重丰饰演的主角\"井之头五郎\"，是个深居简出的小贩.",
    filePath: "内置磁盘/powervnging/系统共享资源中心/孤独大食家/www.dysfcom下载独享资源2005.4k.mp4",
    fileSize: "1.6GB",
    cast: [
        { id: "1", name: "松重丰", role: "井之头五郎", avatar: '' },
        { id: "2", name: "松重丰", role: "井之头五郎", avatar: '' },
        { id: "3", name: "内田朝阳", role: "旁白", avatar: '' },
        { id: "4", name: "野村祐人", role: "导演", avatar: '' },
        { id: "5", name: "村田雄浩", role: "客串", avatar: '' },
        { id: "6", name: "内田朝阳", role: "旁白", avatar: '' },
        { id: "7", name: "野村祐人", role: "导演", avatar: '' },
        { id: "8", name: "村田雄浩", role: "客串", avatar: '' }
    ],
    seasons: [
        {
            id: '1',
            season: 1,
            year: '2013',
            episodes: 13,
            rating: 9.4,
            posterUrl: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR5V6gAbU6KFf9z-jqqV2kk7fJm3VTC1KhzVg&s',
        },
        {
            id: '2',
            season: 2,
            year: '2014',
            episodes: 12,
            rating: 8.1,
            posterUrl: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR5V6gAbU6KFf9z-jqqV2kk7fJm3VTC1KhzVg&s',

        }
    ]
};
const DramasOrMovie: FC<VideoDetailsProps> = ({ videoId }) => {

    const history = useHistory();
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    
    const handleBack = () => {
        history.goBack();
    };

    // 处理焦点元素的滚动
    const handleFocusScroll = (item: any) => {
        if (scrollContainerRef.current && item.ref.current) {
            const container = scrollContainerRef.current;
            const element = item.ref.current;
            const elementRect = element.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            
            // 检查元素是否在可视区域外
            if (elementRect.bottom > containerRect.bottom || elementRect.top < containerRect.top) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    };

    // 确保videoData一定存在
    const data = defaultVideoData;

    return (
        <div className={styles.container}>
            {/* 背景视频/图片 */}
            <div className={styles.videoBackground}>
                {/* 可以使用视频或图片作为背景 */}
                <img src={poster_test} alt={data.title || "视频封面"} />
            </div>
            {/* 可滚动的内容区域 */}
            <div className={styles.scrollableContent} ref={scrollContainerRef}>
                {/* 返回按钮 - 添加 TVFocusable 包裹 */}
                <TVFocusable
                    id="tv-focus-dramasOrMovie-back-button"
                    row={0}
                    col={0}
                    onClick={handleBack}
                    className={styles.backButton}
                    currentItemCallback={handleFocusScroll}
                >
                    <LeftOutlined />
                    <span>返回</span>
                </TVFocusable>

                {/* 视频标题 */}
                <h1 className={styles.title}>{data.title || "未知标题"}</h1>

                {/* 视频信息行 */}
                <div className={styles.infoRow}>
                    <span>{data.category || "-"}</span>
                    <span>{data.region || "-"}</span>
                    <span>{data.duration || "-"}</span>
                </div>

                {/* 视频描述 */}
                <div className={styles.descriptionContainer}>
                    <div className={`${styles.description}`}>
                        {data.description || "暂无描述"}
                    </div>
                </div>
                
                {/* 季度卡片列表 - 使用 TVFocusable 包裹每个卡片 */}
                <div className={styles.film_card_container}>
                    {data.seasons.map((item, index) => (
                        <TVFocusable
                            key={item.id}
                            id={`tv-focus-dramasOrMovie-season-${item.id}`}
                            row={1}
                            col={index}
                            onClick={() => history.push(`/filmAndTelevisionWall_tv/videoDetails`, { isDrama: true })}
                            currentItemCallback={handleFocusScroll}
                        >
                            <FilterFilmCard 
                                title={`第${item.season}季`} 
                                subtitle={item.year} 
                                core={item.rating.toString()} 
                                cover={item.posterUrl} 
                                isLike={false} 
                                isDrama={true} 
                                episodes={item.episodes} 
                                onCardClick={() => history.push(`/filmAndTelevisionWall_tv/videoDetails`, { isDrama: true })} 
                            />
                        </TVFocusable>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default DramasOrMovie;